import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useForm, useFieldArray } from "react-hook-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { ArrowLeft, Save, Eye, Plus, Trash2, Star } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: string;
  unit: string;
  isPopular: boolean;
  features: string[] | string;
}

interface PricingFormData {
  sectionTitle: string;
  sectionDescription: string;
  plans: PricingPlan[];
}

const PricingEdit = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const { register, handleSubmit, control, watch, setValue } = useForm<PricingFormData>({
    defaultValues: {
      sectionTitle: "Bảng giá dịch vụ",
      sectionDescription: "Giá cả minh bạch, không phát sinh chi phí ẩn",
      plans: [
        {
          id: "1",
          name: "Xe 4 chỗ",
          description: "Phù hợp cho gia đình nhỏ",
          price: "15.000",
          unit: "đ/km",
          isPopular: false,
          features: ["Xe đời mới", "Điều hòa mát", "Tài xế thân thiện", "Hỗ trợ 24/7"]
        },
        {
          id: "2",
          name: "Xe 7 chỗ", 
          description: "Thoải mái cho nhóm đông người",
          price: "20.000",
          unit: "đ/km",
          isPopular: true,
          features: ["Xe SUV cao cấp", "Không gian rộng rãi", "Điều hòa 2 chiều", "WiFi miễn phí", "Nước uống miễn phí"]
        }
      ]
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "plans"
  });

  const onSubmit = (data: PricingFormData) => {
    console.log("Pricing data saved:", data);
    toast({
      title: "Đã lưu thành công",
      description: "Bảng giá đã được cập nhật.",
    });
  };

  const addPlan = () => {
    append({
      id: Date.now().toString(),
      name: "Gói mới",
      description: "Mô tả gói mới",
      price: "0",
      unit: "đ/km",
      isPopular: false,
      features: ["Tính năng 1", "Tính năng 2"]
    });
  };

  const togglePopular = (index: number, checked: boolean) => {
    // Chỉ cho phép 1 plan được popular
    if (checked) {
      fields.forEach((_, i) => {
        setValue(`plans.${i}.isPopular`, i === index);
      });
    } else {
      setValue(`plans.${index}.isPopular`, false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate("/admin/content")}
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Chỉnh sửa Bảng giá</h1>
            <p className="text-muted-foreground">Quản lý giá cước các loại xe</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => navigate("/")}>
            <Eye className="w-4 h-4 mr-1" />
            Xem trước
          </Button>
          <Button onClick={handleSubmit(onSubmit)}>
            <Save className="w-4 h-4 mr-1" />
            Lưu thay đổi
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Thông tin chung</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="sectionTitle">Tiêu đề section</Label>
            <Input
              id="sectionTitle"
              {...register("sectionTitle")}
              placeholder="Nhập tiêu đề..."
            />
          </div>
          <div>
            <Label htmlFor="sectionDescription">Mô tả section</Label>
            <Textarea
              id="sectionDescription"
              {...register("sectionDescription")}
              placeholder="Nhập mô tả..."
              rows={2}
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Gói dịch vụ</h2>
        <Button onClick={addPlan}>
          <Plus className="w-4 h-4 mr-1" />
          Thêm gói
        </Button>
      </div>

      <div className="grid gap-6">
        {fields.map((field, index) => (
          <Card key={field.id} className={watch(`plans.${index}.isPopular`) ? "ring-2 ring-primary" : ""}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg flex items-center gap-2">
                  Gói {index + 1}
                  {watch(`plans.${index}.isPopular`) && (
                    <Star className="w-4 h-4 fill-primary text-primary" />
                  )}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={watch(`plans.${index}.isPopular`)}
                      onCheckedChange={(checked) => togglePopular(index, checked)}
                    />
                    <Label className="text-sm">Phổ biến</Label>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => remove(index)}
                    className="text-destructive"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor={`plans.${index}.name`}>Tên gói</Label>
                  <Input
                    {...register(`plans.${index}.name`)}
                    placeholder="Xe 4 chỗ"
                  />
                </div>
                <div>
                  <Label htmlFor={`plans.${index}.description`}>Mô tả ngắn</Label>
                  <Input
                    {...register(`plans.${index}.description`)}
                    placeholder="Phù hợp cho gia đình nhỏ"
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor={`plans.${index}.price`}>Giá</Label>
                  <Input
                    {...register(`plans.${index}.price`)}
                    placeholder="15.000"
                    type="number"
                  />
                </div>
                <div>
                  <Label htmlFor={`plans.${index}.unit`}>Đơn vị</Label>
                  <Input
                    {...register(`plans.${index}.unit`)}
                    placeholder="đ/km"
                  />
                </div>
              </div>
              <div>
                <Label>Tính năng (mỗi dòng 1 tính năng)</Label>
                <Textarea
                  {...register(`plans.${index}.features`)}
                  placeholder="Xe đời mới&#10;Điều hòa mát&#10;Tài xế thân thiện"
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Xem trước</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold">{watch("sectionTitle")}</h2>
              <p className="text-muted-foreground">{watch("sectionDescription")}</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {watch("plans")?.map((plan, index) => (
                <Card key={index} className={`relative ${plan.isPopular ? 'ring-2 ring-primary' : ''}`}>
                  {plan.isPopular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-primary text-primary-foreground px-3 py-1 rounded-full text-sm font-medium">
                        Phổ biến
                      </span>
                    </div>
                  )}
                  <CardContent className="p-6 text-center">
                    <h3 className="text-xl font-bold mb-2">{plan.name}</h3>
                    <p className="text-muted-foreground mb-4">{plan.description}</p>
                    <div className="mb-6">
                      <span className="text-3xl font-bold">{plan.price?.toLocaleString()}</span>
                      <span className="text-muted-foreground ml-1">{plan.unit}</span>
                    </div>
                    <div className="space-y-2 text-left">
                      {Array.isArray(plan.features) ? 
                        plan.features.map((feature, i) => (
                          <div key={i} className="flex items-center text-sm">
                            <div className="w-2 h-2 bg-primary rounded-full mr-2 flex-shrink-0"></div>
                            {feature}
                          </div>
                        )) : 
                        typeof plan.features === 'string' ? plan.features.split('\n').map((feature, i) => (
                          <div key={i} className="flex items-center text-sm">
                            <div className="w-2 h-2 bg-primary rounded-full mr-2 flex-shrink-0"></div>
                            {feature}
                          </div>
                        )) : null
                      }
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PricingEdit;