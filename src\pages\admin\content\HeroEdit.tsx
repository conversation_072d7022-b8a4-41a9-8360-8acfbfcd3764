import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Save, Eye } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface HeroFormData {
  title: string;
  subtitle: string;
  description: string;
  buttonText: string;
  backgroundImage: string;
  stat1Number: string;
  stat1Text: string;
  stat2Number: string;
  stat2Text: string;
  stat3Number: string;
  stat3Text: string;
}

const HeroEdit = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const { register, handleSubmit, watch } = useForm<HeroFormData>({
    defaultValues: {
      title: "TAXI VÂN ĐĂM TOUR",
      subtitle: "Dịch vụ taxi uy tín, an toàn và tiện lợi",
      description: "Chúng tôi cung cấp dịch vụ taxi chất lượng cao với đội ngũ tài xế chuyên nghiệp, xe đời mới và giá cả cạnh tranh.",
      buttonText: "Đặt xe ngay",
      backgroundImage: "/hero-background.jpg",
      stat1Number: "200+",
      stat1Text: "Xe hoạt động",
      stat2Number: "9000+",
      stat2Text: "Khách hàng",
      stat3Number: "24/7",
      stat3Text: "Hỗ trợ"
    }
  });

  const onSubmit = (data: HeroFormData) => {
    console.log("Hero data saved:", data);
    toast({
      title: "Đã lưu thành công",
      description: "Thông tin Hero Section đã được cập nhật.",
    });
  };

  const handlePreview = () => {
    navigate("/");
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate("/admin/content")}
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Chỉnh sửa Hero Section</h1>
            <p className="text-muted-foreground">Banner chính của trang web</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handlePreview}>
            <Eye className="w-4 h-4 mr-1" />
            Xem trước
          </Button>
          <Button onClick={handleSubmit(onSubmit)}>
            <Save className="w-4 h-4 mr-1" />
            Lưu thay đổi
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Nội dung chính</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="title">Tiêu đề chính</Label>
              <Input
                id="title"
                {...register("title")}
                placeholder="Nhập tiêu đề..."
              />
            </div>
            <div>
              <Label htmlFor="subtitle">Tiêu đề phụ</Label>
              <Input
                id="subtitle"
                {...register("subtitle")}
                placeholder="Nhập tiêu đề phụ..."
              />
            </div>
            <div>
              <Label htmlFor="description">Mô tả</Label>
              <Textarea
                id="description"
                {...register("description")}
                placeholder="Nhập mô tả..."
                rows={4}
              />
            </div>
            <div>
              <Label htmlFor="buttonText">Nút hành động</Label>
              <Input
                id="buttonText"
                {...register("buttonText")}
                placeholder="Nhập text button..."
              />
            </div>
            <div>
              <Label htmlFor="backgroundImage">Hình nền (URL)</Label>
              <Input
                id="backgroundImage"
                {...register("backgroundImage")}
                placeholder="Nhập URL hình nền..."
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Thống kê</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="stat1Number">Số liệu 1</Label>
                <Input
                  id="stat1Number"
                  {...register("stat1Number")}
                  placeholder="200+"
                />
              </div>
              <div>
                <Label htmlFor="stat1Text">Mô tả 1</Label>
                <Input
                  id="stat1Text"
                  {...register("stat1Text")}
                  placeholder="Xe hoạt động"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="stat2Number">Số liệu 2</Label>
                <Input
                  id="stat2Number"
                  {...register("stat2Number")}
                  placeholder="9000+"
                />
              </div>
              <div>
                <Label htmlFor="stat2Text">Mô tả 2</Label>
                <Input
                  id="stat2Text"
                  {...register("stat2Text")}
                  placeholder="Khách hàng"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="stat3Number">Số liệu 3</Label>
                <Input
                  id="stat3Number"
                  {...register("stat3Number")}
                  placeholder="24/7"
                />
              </div>
              <div>
                <Label htmlFor="stat3Text">Mô tả 3</Label>
                <Input
                  id="stat3Text"
                  {...register("stat3Text")}
                  placeholder="Hỗ trợ"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Xem trước</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted p-6 rounded-lg">
            <div className="text-center space-y-4">
              <h1 className="text-3xl font-bold">{watch("title")}</h1>
              <h2 className="text-xl text-muted-foreground">{watch("subtitle")}</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">{watch("description")}</p>
              <Button>{watch("buttonText")}</Button>
              <div className="flex justify-center gap-8 mt-6">
                <div className="text-center">
                  <div className="text-2xl font-bold">{watch("stat1Number")}</div>
                  <div className="text-sm text-muted-foreground">{watch("stat1Text")}</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{watch("stat2Number")}</div>
                  <div className="text-sm text-muted-foreground">{watch("stat2Text")}</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{watch("stat3Number")}</div>
                  <div className="text-sm text-muted-foreground">{watch("stat3Text")}</div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default HeroEdit;