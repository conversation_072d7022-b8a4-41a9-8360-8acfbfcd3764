@tailwind base;
@tailwind components;
@tailwind utilities;

/* Van Dam Tour Design System - Vietnamese Taxi Service */

@layer base {
  :root {
    /* Brand Colors - Green Theme for Transportation */
    --background: 0 0% 100%;
    --foreground: 0 0% 13%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 13%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 13%;

    /* Primary Green for CTA and Brand */
    --primary: 122 39% 49%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 122 39% 45%;

    /* Secondary for highlights */
    --secondary: 120 60% 96%;
    --secondary-foreground: 122 39% 49%;

    --muted: 120 14% 96%;
    --muted-foreground: 0 0% 45%;

    --accent: 122 39% 49%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 120 13% 91%;
    --input: 0 0% 100%;
    --ring: 122 39% 49%;

    /* Custom design tokens for Van Dam Tour */
    --success: 122 39% 49%;
    --warning: 45 93% 47%;
    --info: 210 100% 56%;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(122, 39%, 49%), hsl(122, 39%, 45%));
    --gradient-hero: linear-gradient(135deg, hsl(122, 39%, 49%) 0%, hsl(122, 39%, 35%) 100%);
    
    /* Shadows */
    --shadow-soft: 0 4px 6px -1px hsl(122, 39%, 49% / 0.1);
    --shadow-medium: 0 10px 15px -3px hsl(122, 39%, 49% / 0.1);
    --shadow-strong: 0 20px 25px -5px hsl(122, 39%, 49% / 0.15);
    
    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0 0% 7%;
    --foreground: 0 0% 98%;

    --card: 0 0% 9%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 98%;

    --primary: 122 39% 49%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 122 39% 45%;

    --secondary: 0 0% 15%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;

    --accent: 122 39% 49%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 122 39% 49%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Van Dam Tour specific components */
  .hero-bg {
    background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), 
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><rect fill="%23f0f0f0" width="1200" height="800"/><circle fill="%234CAF50" cx="600" cy="400" r="200" opacity="0.1"/></svg>');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
  }

  .glass-card {
    @apply bg-white/95 backdrop-blur-sm border border-white/20 shadow-lg;
  }

  .booking-notification {
    @apply transform transition-all duration-500 ease-in-out;
    animation: slideInNotification 0.5s ease-out;
  }

  .service-card {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-lg cursor-pointer;
  }

  .price-card {
    @apply bg-gradient-to-br from-green-50 to-green-100 border-l-4 border-green-500 p-4 rounded-lg shadow-sm;
  }

  .floating-contact {
    @apply fixed right-4 z-50 flex flex-col gap-3;
    bottom: 2rem;
  }
  
  @media (max-width: 768px) {
    .floating-contact {
      @apply right-2 gap-2;
      bottom: 1rem;
    }
  }

  .floating-btn {
    @apply w-14 h-14 rounded-full shadow-lg flex items-center justify-center text-white font-bold text-lg;
    @apply transition-all duration-300 hover:scale-110 active:scale-95;
  }
  
  @media (max-width: 768px) {
    .floating-btn {
      @apply w-12 h-12 text-base;
    }
  }

  .pulse-ring {
    @apply absolute inset-0 rounded-full border-2 border-green-400 animate-ping;
  }
}

@layer utilities {
  .animate-slide-in-notification {
    animation: slideInNotification 0.5s ease-out;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  .animate-bounce-gentle {
    animation: bounceGentle 2s ease-in-out infinite;
  }
}

@keyframes slideInNotification {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceGentle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}