import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { store, persistor } from "./store/store";
import Index from "./pages/Index";

import Contact from "./pages/Contact";
import News from "./pages/News";
import NewsDetail from "./pages/NewsDetail";
import NotFound from "./pages/NotFound";
import Login from "./pages/admin/Login";
import Dashboard from "./pages/admin/Dashboard";
import AdminLayout from "./components/admin/AdminLayout";
import ContentManagement from "./pages/admin/content/ContentManagement";
import HeroEdit from "./pages/admin/content/HeroEdit";
import ServicesEdit from "./pages/admin/content/ServicesEdit";
import PricingEdit from "./pages/admin/content/PricingEdit";
import Services from "./pages/admin/Services";
import Pricing from "./pages/admin/Pricing";
import AdminNews from "./pages/admin/News";
import Bookings from "./pages/admin/Bookings";
import Media from "./pages/admin/Media";
import Users from "./pages/admin/Users";
import Settings from "./pages/admin/Settings";

const queryClient = new QueryClient();

const App = () => (
  <Provider store={store}>
    <PersistGate loading={<div>Loading...</div>} persistor={persistor}>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/news" element={<News />} />
              <Route path="/news/:id" element={<NewsDetail />} />
              <Route path="/admin/login" element={<Login />} />
              <Route path="/admin" element={<AdminLayout />}>
                <Route path="dashboard" element={<Dashboard />} />
                <Route path="content" element={<ContentManagement />} />
                <Route path="content/hero" element={<HeroEdit />} />
                <Route path="content/services" element={<ServicesEdit />} />
                <Route path="content/pricing" element={<PricingEdit />} />
                <Route path="services" element={<Services />} />
                <Route path="pricing" element={<Pricing />} />
                <Route path="news" element={<AdminNews />} />
                <Route path="bookings" element={<Bookings />} />
                <Route path="media" element={<Media />} />
                <Route path="users" element={<Users />} />
                <Route path="settings" element={<Settings />} />
              </Route>
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </PersistGate>
  </Provider>
);

export default App;
