import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, Car, Calendar, TrendingUp } from "lucide-react";

const Dashboard = () => {
  const stats = [
    {
      title: "Tổng đặt xe",
      value: "1,234",
      change: "+12.5%",
      icon: Calendar,
      color: "text-blue-600"
    },
    {
      title: "Khách hàng",
      value: "856",
      change: "+8.2%", 
      icon: Users,
      color: "text-green-600"
    },
    {
      title: "Xe hoạt động",
      value: "45",
      change: "+2.1%",
      icon: Car,
      color: "text-purple-600"
    },
    {
      title: "<PERSON><PERSON><PERSON> thu tháng",
      value: "125M VNĐ",
      change: "+15.3%",
      icon: TrendingUp,
      color: "text-orange-600"
    }
  ];

  const recentBookings = [
    { id: 1, customer: "Nguyễn Văn A", service: "Taxi sân bay", status: "<PERSON><PERSON><PERSON> thành", time: "2 giờ trước" },
    { id: 2, customer: "Trần Thị B", service: "Taxi đường dài", status: "Đang xử lý", time: "4 giờ trước" },
    { id: 3, customer: "Lê Văn C", service: "Taxi hợp đồng", status: "Chờ xác nhận", time: "6 giờ trước" },
    { id: 4, customer: "Phạm Thị D", service: "Taxi giá rẻ", status: "Hoàn thành", time: "8 giờ trước" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Hoàn thành": return "bg-green-100 text-green-800";
      case "Đang xử lý": return "bg-blue-100 text-blue-800";
      case "Chờ xác nhận": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">Tổng quan hoạt động website</p>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">{stat.change}</span> so với tháng trước
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Bookings */}
      <Card>
        <CardHeader>
          <CardTitle>Đặt xe gần đây</CardTitle>
          <CardDescription>
            Danh sách đặt xe mới nhất trong hệ thống
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentBookings.map((booking) => (
              <div key={booking.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="font-medium">{booking.customer}</div>
                  <div className="text-sm text-muted-foreground">{booking.service}</div>
                </div>
                <div className="flex items-center gap-3">
                  <Badge className={getStatusColor(booking.status)}>
                    {booking.status}
                  </Badge>
                  <div className="text-sm text-muted-foreground">{booking.time}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;